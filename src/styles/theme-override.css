/*
 * Theme override styles to ensure media queries don't override user theme selection
 * This file contains styles that override any @media (prefers-color-scheme) queries
 * to ensure that the user's theme selection takes precedence
 */

/*
 * When light theme is explicitly selected, override any dark mode styles
 * that might be applied by media queries
 */
html.theme-light {
  /* Force light theme styles regardless of system preference */
  color-scheme: light;
}

/*
 * When dark theme is explicitly selected, ensure dark mode styles are applied
 * regardless of system preference
 */
html.theme-dark {
  /* Force dark theme styles regardless of system preference */
  color-scheme: dark;
}

/*
 * Force Tailwind dark mode to respect the theme class on the html element
 * This ensures that dark: variants only apply when the theme-dark class is present
 */
@media (prefers-color-scheme: dark) {
  html:not(.theme-light) .dark\:bg-slate-700 {
    background-color: var(--tw-bg-opacity, 1) * rgba(51, 65, 85, var(--tw-bg-opacity)) !important;
  }

  html.theme-light .dark\:bg-slate-700 {
    background-color: transparent !important;
  }

  html.theme-light .dark\:text-slate-200,
  html.theme-light .dark\:text-slate-100,
  html.theme-light .dark\:text-gray-100,
  html.theme-light .dark\:text-gray-50 {
    color: inherit !important;
  }

  html.theme-light .dark\:border-slate-700,
  html.theme-light .dark\:border-slate-600 {
    border-color: inherit !important;
  }
}

@media (prefers-color-scheme: light) {
  html.theme-dark .dark\:bg-slate-700 {
    background-color: var(--tw-bg-opacity, 1) * rgba(51, 65, 85, var(--tw-bg-opacity)) !important;
  }

  html.theme-dark .dark\:text-slate-200,
  html.theme-dark .dark\:text-slate-100,
  html.theme-dark .dark\:text-gray-100,
  html.theme-dark .dark\:text-gray-50 {
    color: white !important;
  }

  html.theme-dark .dark\:border-slate-700,
  html.theme-dark .dark\:border-slate-600 {
    border-color: rgba(51, 65, 85, var(--tw-border-opacity, 1)) !important;
  }
}

/*
 * Override any @media (prefers-color-scheme: dark) queries for the editor
 * when light theme is explicitly selected
 */
html.theme-light #editor {
  /* Override any media query styles with light theme styles */
  background-color: white !important;
  color: var(--color-gray-900, #1a202c) !important;
}

/*
 * Override any @media (prefers-color-scheme: light) queries for the editor
 * when dark theme is explicitly selected
 */
html.theme-dark #editor {
  /* Override any media query styles with dark theme styles */
  background-color: var(--color-slate-700, #334155) !important;
  color: var(--color-gray-50, #f9fafb) !important;
}

/* Ensure toolbar respects theme */
html.theme-light #toolbar {
  background-color: var(--color-gray-50, #f9fafb) !important;
  border-color: var(--color-gray-200, #e5e7eb) !important;
}

html.theme-dark #toolbar {
  background-color: var(--color-slate-800, #1e293b) !important;
  border-color: var(--color-slate-700, #334155) !important;
}

/* Ensure body respects theme */
html.theme-light body {
  background-color: var(--color-gray-100, #f3f4f6) !important;
  color: var(--color-gray-800, #1f2937) !important;
}

html.theme-dark body {
  background-color: var(--color-gray-900, #111827) !important;
  color: var(--color-gray-100, #f3f4f6) !important;
}

/* Ensure select elements respect theme */
html.theme-light select {
  background-color: white !important;
  color: var(--color-gray-900, #1a202c) !important;
  border-color: var(--color-gray-300, #d1d5db) !important;
}

html.theme-dark select {
  background-color: var(--color-slate-700, #334155) !important;
  color: var(--color-gray-50, #f9fafb) !important;
  border-color: var(--color-slate-600, #475569) !important;
}

/* Ensure toolbar buttons respect theme */
html.theme-light #toolbar button {
  background-color: var(--color-gray-100, #f3f4f6) !important;
  color: var(--color-gray-700, #374151) !important;
  border-color: var(--color-gray-300, #d1d5db) !important;
}

html.theme-light #toolbar button:hover {
  background-color: var(--color-gray-200, #e5e7eb) !important;
}

html.theme-dark #toolbar button {
  background-color: var(--color-slate-700, #334155) !important;
  color: var(--color-slate-200, #e2e8f0) !important;
  border-color: var(--color-slate-600, #475569) !important;
}

html.theme-dark #toolbar button:hover {
  background-color: var(--color-slate-600, #475569) !important;
}

/* Ensure plugin UI elements respect theme */
html.theme-light [role="dialog"],
html.theme-light [role="menu"],
html.theme-light .absolute.bg-white {
  background-color: white !important;
  color: var(--color-gray-900, #1a202c) !important;
  border-color: var(--color-gray-300, #d1d5db) !important;
}

html.theme-dark [role="dialog"],
html.theme-dark [role="menu"],
html.theme-dark .absolute.dark\:bg-slate-700,
html.theme-dark .absolute.dark\:bg-slate-800 {
  background-color: var(--color-slate-700, #334155) !important;
  color: var(--color-slate-200, #e2e8f0) !important;
  border-color: var(--color-slate-600, #475569) !important;
}

/* Ensure plugin UI buttons respect theme */
html.theme-light [role="dialog"] button,
html.theme-light [role="menu"] button {
  color: var(--color-gray-700, #374151) !important;
}

html.theme-dark [role="dialog"] button,
html.theme-dark [role="menu"] button {
  color: var(--color-slate-200, #e2e8f0) !important;
}

/* Ensure plugin UI tabs respect theme */
html.theme-light [role="tablist"] button {
  background-color: var(--color-gray-100, #f3f4f6) !important;
  color: var(--color-gray-700, #374151) !important;
}

html.theme-dark [role="tablist"] button {
  background-color: var(--color-slate-700, #334155) !important;
  color: var(--color-slate-200, #e2e8f0) !important;
}

/* Table Plugin - Enhanced specificity to override media queries */
html.theme-light .feather-table,
html.theme-light .feather-table.theme-light,
html.theme-light table.feather-table {
  background-color: white !important;
  color: var(--color-gray-900, #1a202c) !important;
}

html.theme-light .feather-table td,
html.theme-light .feather-table th,
html.theme-light .feather-table.theme-light td,
html.theme-light .feather-table.theme-light th,
html.theme-light table.feather-table td,
html.theme-light table.feather-table th {
  border-color: var(--color-gray-300, #d1d5db) !important;
  background-color: white !important;
  color: var(--color-gray-900, #1a202c) !important;
}

html.theme-dark .feather-table,
html.theme-dark .feather-table.theme-dark,
html.theme-dark table.feather-table {
  background-color: var(--color-slate-700, #334155) !important;
  color: var(--color-slate-200, #e2e8f0) !important;
}

html.theme-dark .feather-table td,
html.theme-dark .feather-table th,
html.theme-dark .feather-table.theme-dark td,
html.theme-dark .feather-table.theme-dark th,
html.theme-dark table.feather-table td,
html.theme-dark table.feather-table th {
  border-color: var(--color-slate-600, #475569) !important;
  background-color: var(--color-slate-700, #334155) !important;
  color: var(--color-slate-200, #e2e8f0) !important;
}

/* Override any media query styles for tables */
@media (prefers-color-scheme: light) {
  html.theme-dark .feather-table,
  html.theme-dark .feather-table td,
  html.theme-dark .feather-table th {
    background-color: var(--color-slate-700, #334155) !important;
    color: var(--color-slate-200, #e2e8f0) !important;
    border-color: var(--color-slate-600, #475569) !important;
  }
}

@media (prefers-color-scheme: dark) {
  html.theme-light .feather-table,
  html.theme-light .feather-table td,
  html.theme-light .feather-table th {
    background-color: white !important;
    color: var(--color-gray-900, #1a202c) !important;
    border-color: var(--color-gray-300, #d1d5db) !important;
  }
}

/* Presence Plugin */
html.theme-light .feather-presence-bar {
  background-color: var(--color-gray-50, #f9fafb) !important;
  border-color: var(--color-gray-200, #e5e7eb) !important;
}

html.theme-dark .feather-presence-bar {
  background-color: var(--color-slate-800, #1e293b) !important;
  border-color: var(--color-slate-700, #334155) !important;
}

/* Comments Plugin */
html.theme-light .feather-comment-panel {
  background-color: var(--color-gray-50, #f9fafb) !important;
  border-color: var(--color-gray-300, #d1d5db) !important;
  color: var(--color-gray-900, #1a202c) !important;
}

html.theme-dark .feather-comment-panel {
  background-color: var(--color-slate-800, #1e293b) !important;
  border-color: var(--color-slate-700, #334155) !important;
  color: var(--color-slate-200, #e2e8f0) !important;
}

/* Code Block Plugin */
html.theme-light .feather-code-block {
  background-color: var(--color-gray-100, #f3f4f6) !important;
  border-color: var(--color-gray-300, #d1d5db) !important;
  color: var(--color-gray-900, #1a202c) !important;
}

html.theme-dark .feather-code-block {
  background-color: var(--color-slate-800, #1e293b) !important;
  border-color: var(--color-slate-700, #334155) !important;
  color: var(--color-slate-200, #e2e8f0) !important;
}

/* Collapsible Plugin */
html.theme-light .feather-collapsible {
  border-color: var(--color-gray-300, #d1d5db) !important;
  color: var(--color-gray-900, #1a202c) !important;
}

html.theme-dark .feather-collapsible {
  border-color: var(--color-slate-600, #475569) !important;
  color: var(--color-slate-200, #e2e8f0) !important;
}

/* Chart Plugin - Enhanced specificity to override media queries */
html.theme-light canvas.feather-chart,
html.theme-light .feather-chart-wrapper,
html.theme-light canvas.feather-chart.theme-light,
html.theme-light .feather-chart-wrapper.theme-light {
  background-color: white !important;
  border-color: var(--color-gray-200, #e5e7eb) !important;
}

html.theme-dark canvas.feather-chart,
html.theme-dark .feather-chart-wrapper,
html.theme-dark canvas.feather-chart.theme-dark,
html.theme-dark .feather-chart-wrapper.theme-dark {
  background-color: var(--color-slate-900, #0f172a) !important;
  border-color: var(--color-slate-700, #334155) !important;
}

/* Override any media query styles for chart elements */
@media (prefers-color-scheme: light) {
  html.theme-dark canvas.feather-chart,
  html.theme-dark .feather-chart-wrapper {
    background-color: var(--color-slate-900, #0f172a) !important;
    border-color: var(--color-slate-700, #334155) !important;
  }
}

@media (prefers-color-scheme: dark) {
  html.theme-light canvas.feather-chart,
  html.theme-light .feather-chart-wrapper {
    background-color: white !important;
    border-color: var(--color-gray-200, #e5e7eb) !important;
  }
}

/* Math Plugin - Enhanced specificity to override media queries */
html.theme-light .math-inline-wrapper,
html.theme-light .math-display-wrapper,
html.theme-light .katex-container,
html.theme-light .math-inline-wrapper.theme-light,
html.theme-light .math-display-wrapper.theme-light,
html.theme-light .katex-container.theme-light {
  background-color: var(--color-gray-50, #f9fafb) !important;
  border-color: var(--color-gray-200, #e5e7eb) !important;
  color: var(--color-gray-900, #1a202c) !important;
}

html.theme-dark .math-inline-wrapper,
html.theme-dark .math-display-wrapper,
html.theme-dark .katex-container,
html.theme-dark .math-inline-wrapper.theme-dark,
html.theme-dark .math-display-wrapper.theme-dark,
html.theme-dark .katex-container.theme-dark {
  background-color: var(--color-slate-800, #1e293b) !important;
  border-color: var(--color-slate-700, #334155) !important;
  color: var(--color-slate-200, #e2e8f0) !important;
}

/* Override any media query styles for math elements */
@media (prefers-color-scheme: light) {
  html.theme-dark .math-inline-wrapper,
  html.theme-dark .math-display-wrapper,
  html.theme-dark .katex-container {
    background-color: var(--color-slate-800, #1e293b) !important;
    border-color: var(--color-slate-700, #334155) !important;
    color: var(--color-slate-200, #e2e8f0) !important;
  }
}

@media (prefers-color-scheme: dark) {
  html.theme-light .math-inline-wrapper,
  html.theme-light .math-display-wrapper,
  html.theme-light .katex-container {
    background-color: var(--color-gray-50, #f9fafb) !important;
    border-color: var(--color-gray-200, #e5e7eb) !important;
    color: var(--color-gray-900, #1a202c) !important;
  }
}
