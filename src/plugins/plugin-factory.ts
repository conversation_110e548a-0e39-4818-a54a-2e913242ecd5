import type { Editor, Plugin } from '../types';

/**
 * Factory function to create a plugin that conforms to the Plugin interface
 * @param initFn The plugin initialization function
 * @param destroyFn Optional destruction function
 * @returns A Plugin object
 */
export function createPlugin<T>(
  id: string,
  initFn: (editor: Editor) => T,
  destroyFn?: (instance: T) => void
): Plugin {
  let pluginInstance: T | null = null;
  
  return {
    id,
    init: (editor: Editor) => {
      pluginInstance = initFn(editor);
    },
    destroy: () => {
      if (destroyFn && pluginInstance) {
        destroyFn(pluginInstance);
        pluginInstance = null;
      }
    }
  };
}
