import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Find and Replace plugin configuration
 */
const config: PluginConfig = {
  id: 'find-replace',
  name: 'Find and Replace',
  description: 'Search and replace text in the editor content',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'find-replace',
      command: 'find-replace',
      icon: '🔍',
      label: 'Find & Replace',
      tooltip: 'Find and replace text (Ctrl/⌘+F)',
      group: 'utilities',
      ariaLabel: 'Find and replace text',
    }
  ],
  shortcuts: [
    {
      command: 'find-replace',
      key: 'f',
      ctrlKey: true,
      description: 'Open find and replace dialog'
    }
  ]
};

/**
 * Find and Replace plugin implementation
 * Adds ability to search and replace text in the editor
 */
export class FindReplacePlugin extends BasePlugin {
  private dialog: HTMLElement | null = null;
  private isDialogOpen = false;
  private currentMatches: number[] = []; // Store ranges of matches
  private currentMatchIndex = -1;
  
  constructor() {
    super(config);
  }
  
  /**
   * Initialize the plugin
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);
  }
  
  /**
   * Handle the find and replace command
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    if (_command === 'find-replace' && this.editor) {
      if (this.isDialogOpen) {
        this.closeFindReplaceDialog();
      } else {
        this.openFindReplaceDialog();
      }
    }
  }
  
  /**
   * Open the find and replace dialog
   */
  private openFindReplaceDialog(): void {
    if (this.isDialogOpen) {
      return;
    }
    
    // Create dialog
    this.dialog = document.createElement('div');
    this.dialog.className = 'fixed top-5 right-5 w-[320px] bg-white dark:bg-slate-800 rounded-lg shadow-lg p-4 z-[9999] font-sans text-gray-900 dark:text-slate-200';
    this.dialog.setAttribute('role', 'dialog');
    this.dialog.setAttribute('aria-labelledby', 'find-replace-dialog-title');
    
    // Create dialog content programmatically
    const header = document.createElement('div');
    header.className = 'flex items-center justify-between mb-3';
    
    const title = document.createElement('h3');
    title.id = 'find-replace-dialog-title';
    title.className = 'font-semibold text-[15px] m-0';
    title.textContent = 'Find & Replace';
    header.appendChild(title);
    
    const closeButtonDialog = document.createElement('button');
    closeButtonDialog.type = 'button';
    closeButtonDialog.className = 'feather-find-replace-close bg-transparent border-none text-lg cursor-pointer p-1 text-gray-500 dark:text-slate-400 hover:text-gray-700 dark:hover:text-slate-200 leading-none';
    closeButtonDialog.setAttribute('aria-label', 'Close');
    closeButtonDialog.textContent = '×';
    header.appendChild(closeButtonDialog);
    this.dialog.appendChild(header);

    const form = document.createElement('form');
    form.className = 'flex flex-col gap-3';

    // Find Group
    const findGroup = document.createElement('div');
    findGroup.className = 'flex flex-col gap-1';
    const findInputWrapper = document.createElement('div');
    findInputWrapper.className = 'relative flex items-center';
    const findInputElem = document.createElement('input');
    findInputElem.type = 'text';
    findInputElem.id = 'find-input';
    findInputElem.className = 'flex-1 p-2 border border-gray-300 dark:border-slate-600 rounded text-sm bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-1 focus:ring-blue-500 dark:focus:ring-blue-400 focus:outline-none';
    findInputElem.placeholder = 'Find';
    findInputElem.setAttribute('aria-label', 'Find');
    findInputWrapper.appendChild(findInputElem);
    const matchCounterElem = document.createElement('span');
    matchCounterElem.id = 'match-counter';
    matchCounterElem.className = 'absolute right-2 text-gray-500 dark:text-slate-400 text-xs pointer-events-none';
    matchCounterElem.textContent = '0/0';
    findInputWrapper.appendChild(matchCounterElem);
    findGroup.appendChild(findInputWrapper);

    const findOptions = document.createElement('div');
    findOptions.className = 'flex gap-4 mt-1';
    const createOption = (id: string, text: string): HTMLLabelElement => {
      const label = document.createElement('label');
      label.className = 'flex items-center gap-1.5 text-[13px] text-gray-700 dark:text-slate-300 cursor-pointer';
      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.id = id;
      checkbox.className = 'h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 cursor-pointer';
      label.appendChild(checkbox);
      label.appendChild(document.createTextNode(text));
      return label;
    };
    findOptions.appendChild(createOption('case-sensitive-option', 'Case sensitive'));
    findOptions.appendChild(createOption('whole-word-option', 'Whole word'));
    findOptions.appendChild(createOption('regex-option', 'Regex'));
    findGroup.appendChild(findOptions);
    form.appendChild(findGroup);

    // Replace Group
    const replaceGroup = document.createElement('div');
    replaceGroup.className = 'flex flex-col gap-1';
    const replaceInputWrapper = document.createElement('div');
    replaceInputWrapper.className = 'relative flex items-center';
    const replaceInputElem = document.createElement('input');
    replaceInputElem.type = 'text';
    replaceInputElem.id = 'replace-input';
    replaceInputElem.className = 'flex-1 p-2 border border-gray-300 dark:border-slate-600 rounded text-sm bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-1 focus:ring-blue-500 dark:focus:ring-blue-400 focus:outline-none';
    replaceInputElem.placeholder = 'Replace with';
    replaceInputElem.setAttribute('aria-label', 'Replace with');
    replaceInputWrapper.appendChild(replaceInputElem);
    replaceGroup.appendChild(replaceInputWrapper);
    form.appendChild(replaceGroup);

    // Actions Group
    const actionsGroup = document.createElement('div');
    actionsGroup.className = 'flex items-center gap-2 mt-2'; // Adjusted for spacing
    const navButtons = document.createElement('div');
    navButtons.className = 'flex gap-2';
    const prevButtonElem = document.createElement('button');
    prevButtonElem.type = 'button'; prevButtonElem.id = 'prev-match-button';
    prevButtonElem.className = 'py-1.5 px-3 border border-gray-300 dark:border-slate-600 rounded bg-gray-100 dark:bg-slate-700 hover:bg-gray-200 dark:hover:bg-slate-600 cursor-pointer text-[13px] text-gray-800 dark:text-slate-200 disabled:opacity-50 disabled:cursor-not-allowed';
    prevButtonElem.title = 'Previous match'; prevButtonElem.setAttribute('aria-label', 'Previous match'); prevButtonElem.textContent = '↑'; prevButtonElem.disabled = true;
    navButtons.appendChild(prevButtonElem);
    const nextButtonElem = document.createElement('button');
    nextButtonElem.type = 'button'; nextButtonElem.id = 'next-match-button';
    nextButtonElem.className = 'py-1.5 px-3 border border-gray-300 dark:border-slate-600 rounded bg-gray-100 dark:bg-slate-700 hover:bg-gray-200 dark:hover:bg-slate-600 cursor-pointer text-[13px] text-gray-800 dark:text-slate-200 disabled:opacity-50 disabled:cursor-not-allowed';
    nextButtonElem.title = 'Next match'; nextButtonElem.setAttribute('aria-label', 'Next match'); nextButtonElem.textContent = '↓'; nextButtonElem.disabled = true;
    navButtons.appendChild(nextButtonElem);
    actionsGroup.appendChild(navButtons);

    const flexGrowDiv = document.createElement('div');
    flexGrowDiv.className = 'flex-grow';
    actionsGroup.appendChild(flexGrowDiv);

    const replaceButtonElem = document.createElement('button');
    replaceButtonElem.type = 'button'; replaceButtonElem.id = 'replace-button';
    replaceButtonElem.className = 'py-1.5 px-3 border border-gray-300 dark:border-slate-600 rounded bg-gray-100 dark:bg-slate-700 hover:bg-gray-200 dark:hover:bg-slate-600 cursor-pointer text-[13px] text-gray-800 dark:text-slate-200 disabled:opacity-50 disabled:cursor-not-allowed';
    replaceButtonElem.textContent = 'Replace'; replaceButtonElem.disabled = true;
    actionsGroup.appendChild(replaceButtonElem);

    const replaceAllButtonElem = document.createElement('button');
    replaceAllButtonElem.type = 'button'; replaceAllButtonElem.id = 'replace-all-button';
    replaceAllButtonElem.className = 'py-1.5 px-3 rounded cursor-pointer text-[13px] bg-blue-600 hover:bg-blue-700 text-white border border-blue-600 hover:border-blue-700 disabled:opacity-50 disabled:cursor-not-allowed'; // Primary button style
    replaceAllButtonElem.textContent = 'Replace All'; replaceAllButtonElem.disabled = true;
    actionsGroup.appendChild(replaceAllButtonElem);
    form.appendChild(actionsGroup);
    
    this.dialog.appendChild(form);
    
    // Add dialog to document
    document.body.appendChild(this.dialog);
    this.isDialogOpen = true;
    
    // Get form elements
    const findInput = this.dialog.querySelector<HTMLInputElement>('#find-input');
    const replaceInput = this.dialog.querySelector<HTMLInputElement>('#replace-input');
    const caseSensitiveOption = this.dialog.querySelector<HTMLInputElement>('#case-sensitive-option');
    const wholeWordOption = this.dialog.querySelector<HTMLInputElement>('#whole-word-option');
    const regexOption = this.dialog.querySelector<HTMLInputElement>('#regex-option');
    const prevButton = this.dialog.querySelector<HTMLButtonElement>('#prev-match-button');
    const nextButton = this.dialog.querySelector<HTMLButtonElement>('#next-match-button');
    const replaceButton = this.dialog.querySelector<HTMLButtonElement>('#replace-button');
    const replaceAllButton = this.dialog.querySelector<HTMLButtonElement>('#replace-all-button');
    const closeButton = this.dialog.querySelector<HTMLButtonElement>('.feather-find-replace-close');
    const matchCounter = this.dialog.querySelector<HTMLElement>('#match-counter');
    
    // Setup input events
    findInput?.addEventListener('input', () => {
      this.performSearch(
        findInput.value,
        caseSensitiveOption?.checked ?? false,
        wholeWordOption?.checked ?? false,
        regexOption?.checked ?? false
      );
      
      // Enable/disable buttons based on whether there are matches
      const hasMatches = this.currentMatches.length > 0;
      if (prevButton) prevButton.disabled = !hasMatches;
      if (nextButton) nextButton.disabled = !hasMatches;
      if (replaceButton) replaceButton.disabled = !hasMatches;
      if (replaceAllButton) replaceAllButton.disabled = !hasMatches;

      // Update match counter
      if (matchCounter) {
        if (hasMatches) {
          matchCounter.textContent = `${this.currentMatchIndex + 1}/${this.currentMatches.length}`;
        } else {
          matchCounter.textContent = '0/0';
        }
      }
    });
    
    // Setup option changes
    const optionChangeHandler = () => {
      this.performSearch(
        findInput?.value ?? '',
        caseSensitiveOption?.checked ?? false,
        wholeWordOption?.checked ?? false,
        regexOption?.checked ?? false
      );
    };
    
    caseSensitiveOption?.addEventListener('change', optionChangeHandler);
    wholeWordOption?.addEventListener('change', optionChangeHandler);
    regexOption?.addEventListener('change', optionChangeHandler);
    
    // Setup navigation buttons
    prevButton?.addEventListener('click', (e) => {
      e.preventDefault();
      this.navigateToPreviousMatch();
    });
    
    nextButton?.addEventListener('click', (e) => {
      e.preventDefault();
      this.navigateToNextMatch();
    });
    
    // Setup replace buttons
    replaceButton?.addEventListener('click', (e) => {
      e.preventDefault();
      this.replaceCurrentMatch(replaceInput?.value ?? '');
    });
    
    replaceAllButton?.addEventListener('click', (e) => {
      e.preventDefault();
      this.replaceAllMatches(replaceInput?.value ?? '');
    });
    
    // Setup close button
    closeButton?.addEventListener('click', () => {
      this.closeFindReplaceDialog();
    });
    
    // Handle form submission
    // 'form' is already defined in this scope from programmatic creation
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.navigateToNextMatch();
    });
    
    // Handle escape key
    document.addEventListener('keydown', this.handleDialogKeydown);
    
    // Get the selected text if any and use it as the initial search term
    const selection = window.getSelection();
    if (selection && !selection.isCollapsed && selection.toString().trim()) {
      const selectedText = selection.toString().trim();
      if (findInput) findInput.value = selectedText;
      this.performSearch(
        selectedText,
        caseSensitiveOption?.checked ?? false,
        wholeWordOption?.checked ?? false,
        regexOption?.checked ?? false
      );
    }
    
    // Focus the search input
    findInput?.focus();
    findInput?.select();
  }
  
  /**
   * Handle keydown events for the dialog
   */
  private handleDialogKeydown = (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      this.closeFindReplaceDialog();
    }
  };
  
  /**
   * Close the find and replace dialog
   */
  private closeFindReplaceDialog(): void {
    if (!this.isDialogOpen || !this.dialog) return;
    
    // Remove highlights
    this.removeAllHighlights();

    // Remove dialog from DOM
    this.dialog.remove();
    this.dialog = null;
    this.isDialogOpen = false;

    // Restore editor focus and selection
    const editorElement = this.editor?.getElement();
    if (editorElement) {
      editorElement.focus();
      // Restore selection (if needed, implement restoration logic)
      // const savedSelection = this.dialog.dataset.restoreSelection;
      // if (savedSelection) {
      //   const selection = window.getSelection();
      //   selection?.removeAllRanges();
      //   selection?.addRange(JSON.parse(savedSelection));
      // }
    }

    // Reset state
    this.currentMatches = [];
    this.currentMatchIndex = -1;
  }

  /**
   * Perform a search in the editor content
   * @param searchText The text to search for
   * @param caseSensitive Whether the search is case sensitive
   * @param wholeWord Whether to match whole words only
   * @param isRegex Whether the search text is a regular expression
   */
  private performSearch(
    searchText: string,
    caseSensitive: boolean = false,
    wholeWord: boolean = false,
    isRegex: boolean = false
  ): void {
    if (!this.editor || !searchText) {
      this.removeAllHighlights();
      this.currentMatches = [];
      this.currentMatchIndex = -1;
      this.updateMatchCounter();
      this.updateActionButtons();
      return;
    }
    
    const editorElement = this.editor.getElement();
    if (!editorElement) return;

    this.removeAllHighlights();
    this.currentMatches = [];
    this.currentMatchIndex = -1;

    try {
      // Create the search pattern
      let pattern: RegExp;
      
      if (isRegex) {
        // User-provided regex
        const flags = caseSensitive ? 'g' : 'gi';
        pattern = new RegExp(searchText, flags);
      } else {
        // Escape special regex characters if not in regex mode
        const escapedText = searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const wordBoundary = wholeWord ? '\\b' : '';
        const flags = caseSensitive ? 'g' : 'gi';
        pattern = new RegExp(`${wordBoundary}${escapedText}${wordBoundary}`, flags);
      }
      
      // Get editor content as text
      const editorContent = editorElement.textContent ?? '';
      
      // Find all matches
      const matches: number[] = [];
      let match;
      
      while ((match = pattern.exec(editorContent)) !== null) {
        matches.push(match.index);
      }
      
      this.currentMatches = matches;
      
      // Highlight all matches
      this.highlightMatches();
      
      // Navigate to first match if there are any
      if (matches.length > 0) {
        this.navigateToMatch(0);
      }
    } catch (error) {
      console.error('Search error:', error);
      // Handle invalid regex
      if (isRegex && error instanceof SyntaxError) {
        // Could show an error message to the user here
      }
    }
  }
  
  /**
   * Highlight all matches in the editor
   */
  private highlightMatches(): void {
    if (!this.editor) return;
    const editorElement = this.editor.getElement();
    if (!editorElement) return;
    
    const selection = window.getSelection();
    if (!selection) return;
    
    // Create a document fragment to avoid multiple reflows
    const fragment = document.createDocumentFragment();
    let lastIndex = 0;
    
    // Process each match
    this.currentMatches.forEach((matchIndex, i) => {
      // Add text before the match
      if (matchIndex > lastIndex) {
        fragment.appendChild(document.createTextNode(editorElement.textContent?.substring(lastIndex, matchIndex) ?? ''));
      }
      
      // Get the match text (assume it's one character for simplicity)
      // In a real implementation, you'd need to know the length of each match
      const matchLength = 1; // This would need to be calculated from the actual match
      const matchText = editorElement.textContent?.substr(matchIndex, matchLength) ?? ''; // Handle potential null/undefined

      // Create a span for the highlighted match
      const highlightSpan = document.createElement('span');
      // Add a stable JS hook class and base Tailwind highlight classes
      highlightSpan.className = 'js-find-highlight bg-yellow-300 dark:bg-yellow-500/50 rounded-sm';
      highlightSpan.setAttribute('data-match-index', i.toString());
      highlightSpan.textContent = matchText;
      
      fragment.appendChild(highlightSpan);
      
      lastIndex = matchIndex + matchLength;
    });
    
    // Add any remaining text
    const editorTextContent = editorElement.textContent ?? ''; // Handle null
    if (lastIndex < editorTextContent.length) { // Use safe length access
      fragment.appendChild(document.createTextNode(editorTextContent.substring(lastIndex))); // Use safe substring
    }
    
    // Replace the editor content
    editorElement.innerHTML = '';
    editorElement.appendChild(fragment);
  }
  
  /**
   * Remove all highlight spans from the editor
   */
  private removeAllHighlights(): void {
    if (!this.editor) return;
    const editorElement = this.editor.getElement();
    if (!editorElement) return;

    // Get all highlight spans using the JS hook class
    const highlights = editorElement.querySelectorAll<HTMLElement>('.js-find-highlight');
    
    // Replace each highlight with its text content
    highlights.forEach((highlight) => {
      const parent = highlight.parentNode;
      if (parent) {
        parent.replaceChild(document.createTextNode(highlight.textContent ?? ''), highlight); // Handle potential null/undefined
        parent.normalize(); // Merge adjacent text nodes
      }
    });
  }
  
  /**
   * Navigate to a specific match
   * @param index The index of the match to navigate to
   */
  private navigateToMatch(index: number): void {
    if (!this.editor || this.currentMatches.length === 0) return;
    
    // Ensure the index is within bounds
    index = Math.max(0, Math.min(index, this.currentMatches.length - 1));
    
    // Update the current match index
    this.currentMatchIndex = index;

    const baseHighlightClasses = ['bg-yellow-300', 'dark:bg-yellow-500/50'];
    const activeHighlightClasses = ['bg-orange-400', 'dark:bg-orange-600/60', 'outline', 'outline-1', 'outline-orange-600', 'dark:outline-orange-400'];
    
    // Remove active styling from all highlights and ensure base styling
    const allHighlights = this.editor.getElement()?.querySelectorAll<HTMLElement>('.js-find-highlight');
    allHighlights?.forEach((highlight) => {
      highlight.classList.remove('active', ...activeHighlightClasses);
      highlight.classList.add(...baseHighlightClasses); // Ensure base is there
    });
    
    // Add active styling to the current match
    const currentHighlight = this.editor.getElement()?.querySelector<HTMLElement>(`.js-find-highlight[data-match-index="${index}"]`);
    
    if (currentHighlight) {
      currentHighlight.classList.remove(...baseHighlightClasses);
      currentHighlight.classList.add('active', ...activeHighlightClasses);
      
      // Scroll the highlight into view
      currentHighlight.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
      
      // Update the match counter
      if (this.dialog) {
        const matchCounter = this.dialog.querySelector<HTMLElement>('#match-counter');
        if (matchCounter) {
          matchCounter.textContent = `${index + 1}/${this.currentMatches.length}`;
        }
      }
    } else {
      console.warn(`Highlight for match index ${index} not found.`);
      // Potentially re-run search if highlights are out of sync
    }
  }
  
  /**
   * Navigate to the next match
   */
  private navigateToNextMatch(): void {
    if (this.currentMatches.length === 0) return;
    
    let nextIndex = this.currentMatchIndex + 1;
    if (nextIndex >= this.currentMatches.length) {
      nextIndex = 0; // Wrap around to the first match
    }
    
    this.navigateToMatch(nextIndex);
  }
  
  /**
   * Navigate to the previous match
   */
  private navigateToPreviousMatch(): void {
    if (this.currentMatches.length === 0) return;
    
    let prevIndex = this.currentMatchIndex - 1;
    if (prevIndex < 0) {
      prevIndex = this.currentMatches.length - 1; // Wrap around to the last match
    }
    
    this.navigateToMatch(prevIndex);
  }
  
  /**
   * Replace the current match with the replacement text
   * @param replaceText The text to replace the match with
   */
  private replaceCurrentMatch(replaceText: string): void {
    if (!this.editor || this.currentMatches.length === 0 || this.currentMatchIndex < 0) return;

    const editorElement = this.editor.getElement();
    if (!editorElement) return;
    
    // Get the current highlighted match
    const currentHighlight = editorElement.querySelector<HTMLElement>(`.js-find-highlight.active`);

    if (currentHighlight) {
      // Create a text node with the replacement text
      const textNode = document.createTextNode(replaceText);

      // Replace the highlight with the text node
      currentHighlight.parentNode?.replaceChild(textNode, currentHighlight);

      // Trigger input event for history
      editorElement.dispatchEvent(
        new InputEvent('input', { bubbles: true, cancelable: true })
      );

      // Re-run the search to update matches after replacement
      const findInput = this.dialog?.querySelector<HTMLInputElement>('#find-input');
      const caseSensitiveOption = this.dialog?.querySelector<HTMLInputElement>('#case-sensitive-option');
      const wholeWordOption = this.dialog?.querySelector<HTMLInputElement>('#whole-word-option');
      const regexOption = this.dialog?.querySelector<HTMLInputElement>('#regex-option');

      if (findInput) {
        // Get the new index before performing search, as search resets it
        const nextIndex = this.currentMatchIndex;
        
        this.performSearch(
          findInput.value,
          caseSensitiveOption?.checked ?? false,
          wholeWordOption?.checked ?? false,
          regexOption?.checked ?? false
        );

        // Try to navigate to the position where the replacement happened
        if (nextIndex < this.currentMatches.length) {
            this.navigateToMatch(nextIndex);
        } else if (this.currentMatches.length > 0) {
            // If the replaced item was the last one, go to the new last one
            this.navigateToMatch(this.currentMatches.length - 1);
        } // Otherwise, search found nothing, state is already reset
      }
    } else {
        console.warn('Could not find the active highlight to replace.');
    }
  }

  /**
   * Replace all matches with the replacement text
   * @param replaceText The text to replace all matches with
   */
  private replaceAllMatches(replaceText: string): void {
    if (!this.editor || this.currentMatches.length === 0) return;

    const editorElement = this.editor.getElement();
    if (!editorElement) return;

    // Get all highlight spans (use a static NodeList) using the JS hook class
    const highlights = editorElement.querySelectorAll<HTMLElement>('.js-find-highlight');

    // Replace each highlight with the replacement text
    // Iterate backwards to avoid issues with NodeList changing during iteration
    for (let i = highlights.length - 1; i >= 0; i--) {
        const highlight = highlights[i];
        const textNode = document.createTextNode(replaceText);
        highlight.parentNode?.replaceChild(textNode, highlight);
    }

    // Normalize the container to merge adjacent text nodes
    editorElement.normalize();

    // Trigger input event for history
    editorElement.dispatchEvent(
      new InputEvent('input', { bubbles: true, cancelable: true })
    );

    // Reset match state
    this.currentMatches = [];
    this.currentMatchIndex = -1;
  }

  /**
   * Updates the text content of the match counter element.
   */
  private updateMatchCounter(): void {
    const matchCounter = this.dialog?.querySelector<HTMLElement>('#match-counter');
    if (!matchCounter) return;

    const hasMatches = this.currentMatches.length > 0;
    if (hasMatches) {
      matchCounter.textContent = `${this.currentMatchIndex + 1}/${this.currentMatches.length}`;
    } else {
      matchCounter.textContent = '0/0';
    }
  }

  /**
   * Enables or disables action buttons based on the current search state.
   */
  private updateActionButtons(): void {
    const prevButton = this.dialog?.querySelector<HTMLButtonElement>('#prev-match-button');
    const nextButton = this.dialog?.querySelector<HTMLButtonElement>('#next-match-button');
    const replaceButton = this.dialog?.querySelector<HTMLButtonElement>('#replace-button');
    const replaceAllButton = this.dialog?.querySelector<HTMLButtonElement>('#replace-all-button');

    const hasMatches = this.currentMatches.length > 0;
    const canReplace = hasMatches && this.currentMatchIndex !== -1;

    if (prevButton) prevButton.disabled = !hasMatches;
    if (nextButton) nextButton.disabled = !hasMatches;
    if (replaceButton) replaceButton.disabled = !canReplace;
    if (replaceAllButton) replaceAllButton.disabled = !hasMatches;
  }

  /**
   * Clean up when destroyed
   */
  public destroy(): void {
    super.destroy();
    
    // Close dialog if open
    if (this.isDialogOpen) {
      this.closeFindReplaceDialog();
    }
    
    // Remove potential leftover style tag (though it should be external now)
    const styleTag = document.getElementById('feather-find-replace-styles');
    styleTag?.remove(); 
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<FindReplacePlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new FindReplacePlugin();
    instance.init(editor);
    return instance;
  },
  (instance: FindReplacePlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
