import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Word Count plugin configuration
 */
const config: PluginConfig = {
  id: 'word-count',
  name: 'Word Count',
  description: 'Display word count, character count, and reading time statistics',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'word-count',
      command: 'word-count',
      icon: '123',
      label: 'Word Count',
      tooltip: 'Show word count statistics',
      group: 'utilities',
      ariaLabel: 'Show word count statistics',
    }
  ],
  shortcuts: [] // No shortcuts for word count
};

/**
 * Word Count plugin implementation
 * Tracks and displays word count, character count, and estimated reading time
 */
export class WordCountPlugin extends BasePlugin {
  private dialog: HTMLElement | null = null;
  private isDialogOpen = false;
  private statusElement: HTMLElement | null = null;
  private debouncedUpdate: (this: HTMLElement, ev: Event) => any;
  
  constructor() {
    super(config);
    
    // Debounce the update function to avoid excessive updates
    this.debouncedUpdate = this.debounce(this.updateCounts.bind(this), 300);
  }
  
  /**
   * Initialize the plugin
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);
    
    // Create the status element
    this.createStatusElement();
    
    // Initial count update
    this.updateCounts();
    
    // Add listener for content changes
    const editorElement = this.editor?.getElement();
    if (editorElement) {
      editorElement.addEventListener('input', this.debouncedUpdate);
      editorElement.addEventListener('keyup', this.debouncedUpdate);
      editorElement.addEventListener('paste', this.debouncedUpdate);
      editorElement.addEventListener('cut', this.debouncedUpdate);
    }
  }
  
  /**
   * Create a debounced function
   * @param func The function to debounce
   * @param wait Wait time in milliseconds
   * @returns Debounced function
   */
  private debounce(func: (this: HTMLElement, ev: Event) => any, wait: number): (this: HTMLElement, ev: Event) => any {
    let timeout: number | null = null;
    
    return function(this: HTMLElement, ev: Event) {
      const later = () => {
        timeout = null;
        func.call(this, ev);
      };
      
      if (timeout !== null) {
        clearTimeout(timeout);
      }
      
      timeout = window.setTimeout(later, wait);
    };
  }
  
  /**
   * Create the status element that shows word count in the editor
   */
  private createStatusElement(): void {
    if (!this.editor) return;
    
    // Create the status element
    this.statusElement = document.createElement('div');
    this.statusElement.className = 'absolute bottom-0 right-0 py-1.5 px-2.5 bg-gray-50 dark:bg-slate-700 rounded-tl text-xs text-gray-500 dark:text-slate-400 select-none cursor-pointer transition-colors duration-200 z-[100] border-t border-l border-gray-200 dark:border-slate-600 hover:bg-gray-100 dark:hover:bg-slate-600';
    this.statusElement.setAttribute('role', 'button');
    this.statusElement.setAttribute('aria-label', 'Show word count statistics');
    this.statusElement.setAttribute('tabindex', '0');
    
    // Add click handler
    this.statusElement.addEventListener('click', this.handleCommand.bind(this, 'word-count'));
    this.statusElement.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        this.handleCommand('word-count');
      }
    });
    
    // Position it relative to the editor
    const editorContainer = this.editor.getElement()?.parentElement;
    if (editorContainer) {
      // Make sure the container is positioned relatively
      const computedStyle = window.getComputedStyle(editorContainer);
      if (computedStyle.position === 'static') {
        editorContainer.style.position = 'relative';
      }
      
      editorContainer.appendChild(this.statusElement);
    } else {
      // Fallback to adding it to the editor itself
      const editorElement = this.editor.getElement();
      if (editorElement) {
        editorElement.style.position = 'relative';
        editorElement.appendChild(this.statusElement);
      }
    }
  }
  
  /**
   * Update the word and character counts
   */
  private updateCounts(): void {
    const editorElement = this.editor?.getElement();
    if (!editorElement || !this.statusElement) return;
    
    // Get the text content of the editor
    const content = editorElement.textContent || '';
    
    // Count words (split by whitespace and filter out empty strings)
    const words = content.trim().split(/\s+/).filter(word => word.length > 0);
    const wordCount = words.length;
    
    // Count characters (without spaces)
    const charCount = content.replace(/\s/g, '').length;
    
    // Update the status element
    this.statusElement.textContent = `${wordCount} words`;
    
    // Update the dialog if it's open
    this.updateDialogContent(wordCount, charCount, content);
  }
  
  /**
   * Update the content of the word count dialog
   * @param wordCount Number of words
   * @param charCount Number of characters (without spaces)
   * @param content The full content for calculating reading time
   */
  private updateDialogContent(wordCount: number, charCount: number, content: string): void {
    if (!this.dialog || !this.isDialogOpen) return;
    
    // Calculate reading time (average reading speed is ~200-250 words per minute)
    const readingTimeMinutes = wordCount / 225;
    let readingTimeText = '';
    
    if (readingTimeMinutes < 1) {
      // Convert to seconds
      const seconds = Math.ceil(readingTimeMinutes * 60);
      readingTimeText = `${seconds} second${seconds !== 1 ? 's' : ''}`;
    } else if (readingTimeMinutes < 60) {
      // Round to nearest minute, or use decimal for < 5 minutes
      if (readingTimeMinutes < 5) {
        readingTimeText = `${readingTimeMinutes.toFixed(1)} minute${readingTimeMinutes !== 1 ? 's' : ''}`;
      } else {
        const minutes = Math.round(readingTimeMinutes);
        readingTimeText = `${minutes} minute${minutes !== 1 ? 's' : ''}`;
      }
    } else {
      // Hours and minutes
      const hours = Math.floor(readingTimeMinutes / 60);
      const minutes = Math.round(readingTimeMinutes % 60);
      readingTimeText = `${hours} hour${hours !== 1 ? 's' : ''} ${minutes} minute${minutes !== 1 ? 's' : ''}`;
    }
    
    // Count characters with spaces
    const charCountWithSpaces = content.length;
    
    // Update the stats in the dialog
    const wordCountValue = this.dialog.querySelector('#word-count-value');
    const charCountValue = this.dialog.querySelector('#char-count-value');
    const charWithSpacesValue = this.dialog.querySelector('#char-with-spaces-value');
    const readingTimeValue = this.dialog.querySelector('#reading-time-value');
    
    if (wordCountValue) wordCountValue.textContent = wordCount.toString();
    if (charCountValue) charCountValue.textContent = charCount.toString();
    if (charWithSpacesValue) charWithSpacesValue.textContent = charCountWithSpaces.toString();
    if (readingTimeValue) readingTimeValue.textContent = readingTimeText;
  }
  
  /**
   * Handle the word count command
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    if (_command === 'word-count') {
      if (this.isDialogOpen) {
        this.closeWordCountDialog();
      } else {
        this.openWordCountDialog();
      }
    }
  }
  
  /**
   * Open the word count dialog
   */
  private openWordCountDialog(): void {
    if (this.isDialogOpen) return;
    
    // Create dialog
    this.dialog = document.createElement('div');
    this.dialog.className = 'fixed bottom-10 right-5 w-[280px] bg-white dark:bg-slate-800 rounded-lg shadow-lg p-4 z-[9999] font-sans text-gray-900 dark:text-slate-200 flex flex-col';
    this.dialog.setAttribute('role', 'dialog');
    this.dialog.setAttribute('aria-labelledby', 'word-count-dialog-title');
    
    // Create dialog content programmatically
    // Header
    const header = document.createElement('div');
    header.className = 'flex items-center justify-between mb-4 pb-3 border-b border-gray-200 dark:border-slate-700';
    const title = document.createElement('h3');
    title.id = 'word-count-dialog-title';
    title.className = 'font-semibold text-base m-0';
    title.textContent = 'Document Statistics';
    const closeButtonDialog = document.createElement('button');
    closeButtonDialog.type = 'button';
    closeButtonDialog.className = 'bg-transparent border-none text-xl cursor-pointer p-1 text-gray-500 dark:text-slate-400 hover:text-gray-700 dark:hover:text-slate-200 leading-none';
    closeButtonDialog.setAttribute('aria-label', 'Close');
    closeButtonDialog.textContent = '×';
    header.appendChild(title);
    header.appendChild(closeButtonDialog);
    this.dialog.appendChild(header);

    // Stats Grid
    const statsGrid = document.createElement('div');
    statsGrid.className = 'grid grid-cols-3 gap-3 mb-4 text-center';

    const createStatBox = (id: string, labelText: string, initialValue: string = '0'): HTMLDivElement => {
      const statBox = document.createElement('div');
      statBox.className = 'p-2.5 bg-gray-50 dark:bg-slate-700 rounded';
      const valueDiv = document.createElement('div');
      valueDiv.id = id;
      valueDiv.className = 'text-lg font-semibold text-gray-800 dark:text-slate-100 mb-1';
      valueDiv.textContent = initialValue;
      const labelDiv = document.createElement('div');
      labelDiv.className = 'text-xs text-gray-500 dark:text-slate-400';
      labelDiv.textContent = labelText;
      statBox.appendChild(valueDiv);
      statBox.appendChild(labelDiv);
      return statBox;
    };

    statsGrid.appendChild(createStatBox('word-count-value', 'Words'));
    statsGrid.appendChild(createStatBox('char-count-value', 'Characters (no spaces)'));
    statsGrid.appendChild(createStatBox('char-with-spaces-value', 'Characters (with spaces)'));
    this.dialog.appendChild(statsGrid);

    // Reading Time
    const readingTimeContainer = document.createElement('div');
    readingTimeContainer.className = 'text-center p-2.5 bg-gray-50 dark:bg-slate-700 rounded mt-3';
    const readingTimeValueDiv = document.createElement('div');
    readingTimeValueDiv.id = 'reading-time-value';
    readingTimeValueDiv.className = 'text-lg font-semibold text-gray-800 dark:text-slate-100 mb-1';
    readingTimeValueDiv.textContent = '0 seconds';
    const readingTimeLabelDiv = document.createElement('div');
    readingTimeLabelDiv.className = 'text-xs text-gray-500 dark:text-slate-400';
    readingTimeLabelDiv.textContent = 'Reading Time';
    readingTimeContainer.appendChild(readingTimeValueDiv);
    readingTimeContainer.appendChild(readingTimeLabelDiv);
    this.dialog.appendChild(readingTimeContainer);
    
    // Add dialog to document
    document.body.appendChild(this.dialog);
    this.isDialogOpen = true;
    
    // Update stats
    this.updateCounts();
    
    // Setup close button (now named closeButtonDialog)
    closeButtonDialog.addEventListener('click', () => {
      this.closeWordCountDialog();
    });
    
    // Handle escape key
    document.addEventListener('keydown', this.handleDialogKeydown);
  }
  
  /**
   * Handle keydown events for the dialog
   */
  private handleDialogKeydown = (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      this.closeWordCountDialog();
    }
  };
  
  /**
   * Close the word count dialog
   */
  private closeWordCountDialog(): void {
    if (!this.isDialogOpen) return;
    
    // Remove dialog
    if (this.dialog && this.dialog.parentNode) {
      this.dialog.parentNode.removeChild(this.dialog);
    }
    
    this.dialog = null;
    this.isDialogOpen = false;
    
    // Remove event listener
    document.removeEventListener('keydown', this.handleDialogKeydown);
  }
  
  /**
   * Clean up when destroyed
   */
  public destroy(): void {
    super.destroy();
    
    // Close dialog if open
    if (this.isDialogOpen) {
      this.closeWordCountDialog();
    }
    
    // Remove status element
    if (this.statusElement?.parentNode) {
      this.statusElement.parentNode.removeChild(this.statusElement);
    }
    
    // Remove event listeners
    const editorElement = this.editor?.getElement();
    if (editorElement) {
      editorElement.removeEventListener('input', this.debouncedUpdate);
      editorElement.removeEventListener('keyup', this.debouncedUpdate);
      editorElement.removeEventListener('paste', this.debouncedUpdate);
      editorElement.removeEventListener('cut', this.debouncedUpdate);
    }
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<WordCountPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new WordCountPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: WordCountPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
