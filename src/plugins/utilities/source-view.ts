import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Source View plugin configuration
 */
const config: PluginConfig = {
  id: 'source-view',
  name: 'Source View',
  description: 'View and edit the HTML source code of the editor content',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'source-view',
      command: 'source-view',
      icon: '</>',
      label: 'Source',
      tooltip: 'View HTML source code',
      group: 'utilities',
      ariaLabel: 'View HTML source code',
    }
  ],
  shortcuts: [] // No shortcuts for source view
};

/**
 * Source View plugin implementation
 * Allows viewing and editing the HTML source code of the editor content
 */
export class SourceViewPlugin extends BasePlugin {
  private dialog: HTMLElement | null = null;
  private isDialogOpen = false;
  private isViewOnly = true;
  private originalContent: string = '';
  private toolbarButton: HTMLElement | null = null;
  
  constructor() {
    super(config);
  }
  
  /**
   * Initialize the plugin
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);
    
    // Find the toolbar button
    this.findToolbarButton();
  }
  
  /**
   * Find the toolbar button after it's registered
   */
  private findToolbarButton(): void {
    const toolbar = document.getElementById('toolbar');
    if (!toolbar) return;
    
    // Find the source view button
    this.toolbarButton = toolbar.querySelector('[data-command="source-view"]');
  }
  
  /**
   * Handle the source view command
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    if (_command !== 'source-view' || this.isDialogOpen || !this.editor) {
      return; // Ignore if not the right command, dialog already open, or no editor
    }
    
    // If the dialog is closed, open it
    this.openSourceViewDialog();
  }
  
  /**
   * Open the source view dialog
   */
  private openSourceViewDialog(): void {
    if (this.isDialogOpen) return;
    if (!this.editor) return; // Ensure editor exists
    
    // Store the original content
    const editorElement = this.editor.getElement();
    if (!editorElement) return;
    this.originalContent = editorElement.innerHTML;
    
    // Create backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'fixed inset-0 w-full h-full bg-black/40 z-[9998]'; // Tailwind for backdrop
    document.body.appendChild(backdrop);
    
    // Create dialog
    this.dialog = document.createElement('div');
    this.dialog.className = 'fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-4/5 h-4/5 max-w-[1000px] max-h-[800px] bg-white dark:bg-slate-800 rounded-lg shadow-xl p-4 flex flex-col z-[9999] font-sans text-gray-900 dark:text-slate-200';
    this.dialog.setAttribute('role', 'dialog');
    this.dialog.setAttribute('aria-labelledby', 'source-view-dialog-title');
    
    // Format the HTML content for better readability
    const formattedHtml = this.formatHtml(this.originalContent);
    
    // Create dialog content programmatically
    // Header
    const header = document.createElement('div');
    header.className = 'flex items-center justify-between mb-3';
    
    const title = document.createElement('h3');
    title.id = 'source-view-dialog-title';
    title.className = 'font-semibold text-base m-0';
    title.textContent = 'HTML Source Code';
    header.appendChild(title);
    
    const headerActions = document.createElement('div');
    headerActions.className = 'flex gap-2';
    
    const toggleEditButton = document.createElement('button');
    toggleEditButton.type = 'button';
    toggleEditButton.id = 'toggle-edit-button';
    toggleEditButton.className = 'py-1.5 px-2.5 rounded cursor-pointer text-sm bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-slate-200 hover:bg-gray-200 dark:hover:bg-slate-600';
    toggleEditButton.textContent = this.isViewOnly ? 'Enable Editing' : 'Read Only';
    headerActions.appendChild(toggleEditButton);
    
    const closeButtonDialog = document.createElement('button');
    closeButtonDialog.type = 'button';
    closeButtonDialog.className = 'bg-transparent border-none py-1.5 px-2.5 rounded cursor-pointer text-sm text-gray-500 dark:text-slate-400 hover:bg-gray-200 dark:hover:bg-slate-700';
    closeButtonDialog.setAttribute('aria-label', 'Close');
    closeButtonDialog.textContent = 'Close'; // More descriptive than '×'
    headerActions.appendChild(closeButtonDialog);
    header.appendChild(headerActions);
    this.dialog.appendChild(header);

    // Editor area (textarea container)
    const editorDiv = document.createElement('div');
    editorDiv.className = 'flex-1 overflow-hidden border border-gray-300 dark:border-slate-600 rounded';
    const textarea = document.createElement('textarea');
    textarea.id = 'source-textarea';
    textarea.className = `w-full h-full p-3 border-none resize-none outline-none font-mono text-[13px] leading-snug bg-gray-50 dark:bg-slate-700 text-gray-900 dark:text-slate-100 ${this.isViewOnly ? 'read-only:bg-white dark:read-only:bg-slate-800' : ''}`;
    textarea.readOnly = this.isViewOnly;
    textarea.setAttribute('aria-readonly', this.isViewOnly.toString());
    textarea.spellcheck = false;
    textarea.wrap = 'soft';
    textarea.value = formattedHtml;
    editorDiv.appendChild(textarea);
    this.dialog.appendChild(editorDiv);

    // Footer
    const footer = document.createElement('div');
    footer.className = 'flex items-center justify-between mt-3'; // justify-between for status and button
    
    const editorStatus = document.createElement('div');
    editorStatus.id = 'editor-status';
    editorStatus.className = 'text-xs text-gray-500 dark:text-slate-400';
    editorStatus.textContent = this.isViewOnly ? 'View only mode. Enable editing to make changes.' : 'Edit mode. Changes will not be applied until you click Apply.';
    footer.appendChild(editorStatus);
    
    const applyButton = document.createElement('button');
    applyButton.type = 'button';
    applyButton.id = 'apply-button';
    applyButton.className = 'py-2 px-4 rounded border-none text-sm cursor-pointer bg-blue-600 hover:bg-blue-700 text-white disabled:bg-gray-400 dark:disabled:bg-slate-600 disabled:cursor-not-allowed';
    applyButton.disabled = this.isViewOnly;
    applyButton.textContent = 'Apply Changes';
    footer.appendChild(applyButton);
    this.dialog.appendChild(footer);
    
    // Add dialog to document
    document.body.appendChild(this.dialog);
    this.isDialogOpen = true;
    
    // Update the toolbar button state
    if (this.toolbarButton) {
      this.toolbarButton.classList.add('active');
    }
    
    // Event listeners are attached to elements created programmatically above.
    // 'toggleEditButton', 'applyButton', 'closeButtonDialog', 'textarea', 'editorStatus' are already in scope.
    
    // Setup toggle edit button
    // Note: 'toggleEditButton' refers to the variable from programmatic creation above.
    toggleEditButton.addEventListener('click', () => {
      this.isViewOnly = !this.isViewOnly;
      
      // Update UI
      toggleEditButton.textContent = this.isViewOnly ? 'Enable Editing' : 'Read Only';
      textarea.readOnly = this.isViewOnly;
      textarea.setAttribute('aria-readonly', this.isViewOnly ? 'true' : 'false');
      
      if (this.isViewOnly) {
        textarea.classList.add('readonly');
        applyButton.disabled = true;
        editorStatus.textContent = 'View only mode. Enable editing to make changes.';
      } else {
        textarea.classList.remove('readonly');
        applyButton.disabled = false;
        editorStatus.textContent = 'Edit mode. Changes will not be applied until you click Apply.';
        textarea.focus();
      }
    });
    
    // Setup apply button
    applyButton.addEventListener('click', () => {
      if (!this.editor) return;
      
      try {
        const currentEditorElement = this.editor.getElement();
        if (!currentEditorElement) throw new Error('Editor element not available to apply HTML.');
        
        currentEditorElement.innerHTML = textarea.value;
        
        // Trigger input event for history
        currentEditorElement.dispatchEvent(
          new InputEvent('input', { bubbles: true, cancelable: true })
        );
        
        // Close the dialog
        this.closeSourceViewDialog();
      } catch (error) {
        console.error('Error applying HTML:', error);
        // Could show an error message to the user here
      }
    });
    
    // Setup close button
    // Note: 'closeButtonDialog' refers to the variable from programmatic creation above.
    closeButtonDialog.addEventListener('click', () => {
      this.closeSourceViewDialog();
    });
    
    // Close on backdrop click
    backdrop.addEventListener('click', () => {
      this.closeSourceViewDialog();
    });
    
    // Handle escape key
    document.addEventListener('keydown', this.handleDialogKeydown);
    
    // Focus the textarea
    textarea.focus();
  }
  
  /**
   * Format HTML code for better readability
   * @param html The HTML to format
   * @returns Formatted HTML
   */
  private formatHtml(html: string): string {
    // Simple HTML formatter
    // In a real implementation, you might want to use a more robust HTML formatter library
    
    const indentSize = 2;
    let formattedHtml = '';
    let indentLevel = 0;
    
    // Replace all opening tags
    html = html.replace(/(<[^/].*?>)/g, '\n$1');
    
    // Replace all closing tags
    html = html.replace(/(<\/.*?>)/g, '$1\n');
    
    // Split by newline and process each line
    const lines = html.split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;
      
      // Check if it's a closing tag
      if (line.match(/^<\//)) {
        indentLevel--;
      }
      
      // Add indentation
      const indent = ' '.repeat(indentLevel * indentSize);
      formattedHtml += indent + line + '\n';
      
      // Check if it's an opening tag and not self-closing
      if (line.match(/^<[^/]/) && !line.match(/\/>$/)) {
        // Don't increase indent level for self-closing tags or tags that don't need closing tags
        if (!line.match(/<(img|br|hr|input|link|meta)\b/i)) {
          indentLevel++;
        }
      }
    }
    
    return formattedHtml;
  }
  
  /**
   * Handle keydown events for the dialog
   */
  private handleDialogKeydown = (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      this.closeSourceViewDialog();
    }
  };
  
  /**
   * Close the source view dialog
   */
  private closeSourceViewDialog(): void {
    if (!this.isDialogOpen) return;
    
    // Remove backdrop
    const backdrop = document.querySelector('.feather-source-view-backdrop');
    if (backdrop && backdrop.parentNode) {
      backdrop.parentNode.removeChild(backdrop);
    }
    
    // Remove dialog
    if (this.dialog && this.dialog.parentNode) {
      this.dialog.parentNode.removeChild(this.dialog);
    }
    
    this.dialog = null;
    this.isDialogOpen = false;
    
    // Update the toolbar button state
    if (this.toolbarButton) {
      this.toolbarButton.classList.remove('active');
    }
    
    // Remove event listener
    document.removeEventListener('keydown', this.handleDialogKeydown);
    
    // Focus back to editor
    if (this.editor) {
      this.editor.focus();
    }
  }
  
  /**
   * Clean up when destroyed
   */
  public destroy(): void {
    super.destroy();
    
    // Close dialog if open
    if (this.isDialogOpen) {
      this.closeSourceViewDialog();
    }
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<SourceViewPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new SourceViewPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: SourceViewPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
