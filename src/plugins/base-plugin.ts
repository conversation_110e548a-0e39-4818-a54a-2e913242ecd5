import type { Editor } from '../types';

/**
 * Base interface for all FeatherJS plugins
 * Ensures consistent API across all plugins
 */
export interface PluginConfig {
  id: string;
  name: string;
  description?: string;
  version?: string;
  toolbarItems?: ToolbarItemConfig[];
  shortcuts?: ShortcutConfig[];
}

/**
 * Configuration for toolbar items
 */
export interface ToolbarItemConfig {
  id: string;
  command: string;
  icon: string;
  label: string;
  tooltip?: string;
  group?: string;
  position?: number;
  ariaLabel?: string;
  ariaRole?: string;
  component?: string;
  type?: string;
}

/**
 * Configuration for keyboard shortcuts
 */
export interface ShortcutConfig {
  command: string;
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  metaKey?: boolean;
  preventDefault?: boolean;
  description?: string;
}

/**
 * Base class for all FeatherJS plugins
 * Provides common functionality and ensures consistent API
 */
export abstract class BasePlugin {
  protected editor?: Editor;
  protected config: PluginConfig;
  protected toolbarButtons: HTMLElement[] = [];
  protected shortcutHandlers: { [key: string]: (event: KeyboardEvent) => void } = {};

  constructor(config: PluginConfig) {
    this.config = config;
  }

  /**
   * Initialize the plugin with the editor instance
   * @param editor The editor instance
   */
  init(editor: Editor): void {
    this.editor = editor;
    this.registerToolbarItems();
    this.registerShortcuts();
    this.onInit();
  }

  /**
   * Register toolbar items for this plugin
   * @protected
   */
  protected registerToolbarItems(): void {
    const toolbar = document.getElementById('toolbar');
    if (!toolbar) {
      console.error(`Toolbar element not found for plugin ${this.config.id}`);
      return;
    }

    this.config.toolbarItems?.forEach(item => {
      const button = document.createElement('button');
      button.type = 'button';
      // Base Tailwind classes for toolbar buttons
      button.className = [
        'p-2', // Padding
        'rounded-md', // Rounded corners
        'text-sm', // Font size
        'font-medium', // Font weight
        'transition-colors', 'duration-150', 'ease-in-out', // Transitions
        'bg-gray-100', 'text-gray-700', // Light mode base
        'hover:bg-gray-200', // Light mode hover
        'dark:bg-slate-700', 'dark:text-slate-200', // Dark mode base
        'dark:hover:bg-slate-600', // Dark mode hover
        'focus-visible:outline-none', 'focus-visible:ring-2', 'focus-visible:ring-blue-500', // Focus visible light
        'dark:focus-visible:ring-blue-400', // Focus visible dark
        'focus-visible:ring-offset-2', 'dark:focus-visible:ring-offset-slate-800' // Focus visible offset
      ].join(' ');

      button.setAttribute('data-command', item.command);
      button.innerHTML = item.icon; // Use icon as button content

      button.setAttribute('aria-label', item.ariaLabel || item.label);

      if (item.ariaRole) {
        button.setAttribute('role', item.ariaRole);
      }

      if (item.group) {
        button.setAttribute('data-group', item.group);
      }

      if (item.tooltip) {
        button.setAttribute('title', item.tooltip);
      }

      // Add the button to the toolbar
      toolbar.appendChild(button);
      this.toolbarButtons.push(button); // Still store as HTMLElement

      // Add event listener for the button
      button.addEventListener('click', () => { // Changed to 'click'
        this.handleCommand(item.command);
        // The active class and animation will be handled by individual plugins
        // For example, a plugin might do: button.classList.toggle('active-pulse-class');
        // where 'active-pulse-class' could be 'animate-button-pulse'
        if (this.editor) {
          this.editor.focus();
        }
      });
    });
  }

  /**
   * Register keyboard shortcuts for this plugin
   * @protected
   */
  protected registerShortcuts(): void {
    this.config.shortcuts?.forEach(shortcut => {
      const handler = (event: KeyboardEvent) => {
        // Check if the key combination matches
        const keyMatches = event.key.toLowerCase() === shortcut.key.toLowerCase();
        const ctrlMatches = Boolean(event.ctrlKey) === Boolean(shortcut.ctrlKey);
        const altMatches = Boolean(event.altKey) === Boolean(shortcut.altKey);
        const shiftMatches = Boolean(event.shiftKey) === Boolean(shortcut.shiftKey);
        const metaMatches = Boolean(event.metaKey) === Boolean(shortcut.metaKey);

        if (keyMatches && ctrlMatches && altMatches && shiftMatches && metaMatches) {
          if (shortcut.preventDefault !== false) {
            event.preventDefault();
          }
          this.handleCommand(shortcut.command);
        }
      };

      this.shortcutHandlers[shortcut.command] = handler;
      document.addEventListener('keydown', handler);
    });
  }

  /**
   * Internal method to handle keyboard shortcut commands
   * To be implemented by subclasses
   * @param _command The command to handle
   */
  protected abstract handleCommand(_command: string): void;

  /**
   * Called when the plugin is initialized
   * Override this method to add custom initialization logic
   * @protected
   */
  protected onInit(): void {
    // Override in subclasses
  }

  /**
   * Clean up resources when the plugin is destroyed
   */
  destroy(): void {
    // Remove toolbar buttons
    const toolbar = document.getElementById('toolbar');
    if (toolbar) {
      this.toolbarButtons.forEach(button => {
        toolbar.removeChild(button);
      });
    }

    // Remove event listeners
    Object.entries(this.shortcutHandlers).forEach(([_command, handler]) => {
      document.removeEventListener('keydown', handler);
    });

    this.onDestroy();
  }

  /**
   * Called when the plugin is destroyed
   * Override this method to add custom cleanup logic
   * @protected
   */
  protected onDestroy(): void {
    // Override in subclasses
  }
}
